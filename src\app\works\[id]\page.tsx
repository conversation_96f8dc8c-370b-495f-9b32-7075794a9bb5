'use client';

import React, { useEffect, useState, useMemo, useRef, useCallback } from 'react';

import { useParams, useRouter } from 'next/navigation';
import { getWorkById, updateWork, Work } from '@/data';
import BackButton from '@/components/BackButton';
import TopBar from '@/components/TopBar';
import Sidebar from '@/components/Sidebar';
import { AIOverlayPanel } from '@/components/works/AIOverlayPanel';
import { KnowledgeModal } from '@/components/knowledgebase/KnowledgeModal';
import { CharacterCardModal } from '@/components/works/CharacterCardModal';
import { Character } from '@/types/character';
import { Chapter } from '@/types/chapter';
import { Outline } from '@/types/outline';
import { Setting } from '@/types/setting';
import { Knowledge } from '@/types/knowledge';
import { workContentUtils } from '@/lib/utils';
import { getCurrentEditContent, EditMode } from '@/utils/editContentUtils';

// 类型定义
interface DebouncedFunction<T extends (...args: any[]) => any> {
  (...args: Parameters<T>): void;
  cancel: () => void;
}

// 防抖函数
const debounce = <T extends (...args: any[]) => any>(func: T, delay: number): DebouncedFunction<T> => {
  let timeoutId: NodeJS.Timeout | null = null;

  // 创建一个包含函数主体和cancel方法的对象
  const debouncedFn = ((...args: Parameters<T>) => {
    if (timeoutId) clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  }) as DebouncedFunction<T>;

  // 显式添加cancel方法
  debouncedFn.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  return debouncedFn;
};

// 章节类型定义已移至 @/types/chapter

// 富文本编辑器组件
const RichTextEditor = ({
  content,
  onChange,
  onSave,
  isSaving,
  chapters,
  activeChapter,
  workId,
  lastSavedAt,
  isDescending, // 添加排序状态
  onOpenCharacterModal, // 添加角色卡模态窗口回调
  onInsertToEditor, // 添加内容插入回调
  editMode, // 添加编辑模式参数
}: {
  content: string;
  onChange: (content: string) => void;
  onSave: () => void;
  isSaving: boolean;
  chapters: Chapter[];
  activeChapter: number;
  workId: number;
  lastSavedAt: Date | null;
  isDescending: boolean; // 添加排序状态类型
  onOpenCharacterModal: () => void; // 添加角色卡模态窗口回调类型
  onInsertToEditor: (content: string) => void; // 添加内容插入回调类型
  editMode: 'chapter' | 'character' | 'outline' | 'setting' | 'knowledge'; // 编辑模式类型
}) => {
  // 选中文本相关状态
  const [selectedText, setSelectedText] = useState('');
  const [showPolishModal, setShowPolishModal] = useState(false);

  // 知识库状态
  const [showArchiveModal, setShowArchiveModal] = useState(false);

  // 打开AI润色模态窗口
  const openPolishModal = () => {
    // 获取textarea元素
    const textarea = document.querySelector('textarea');
    if (!textarea) {
      return; // 如果找不到编辑器元素，静默返回
    }

    // 直接从文本域获取选中的文本
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    console.log('Selection range:', start, end);

    // 如果有选中文本
    if (start !== end) {
      const text = textarea.value.substring(start, end).trim();
      console.log('Selected text from textarea:', text);

      if (text.length > 0) {
        // 先设置选中的文本，然后打开AI辅助窗口
        setSelectedText(text);
        // 打开AI覆盖层面板，设置初始类型为润色
        setTimeout(() => {
          setShowAIOverlay(true);
        }, 0);
      }
      // 如果选中的文本为空，不做任何反应
    }
    // 如果没有选中文本，不做任何反应
  };

  // 打开知识库模态窗口
  const openArchiveModal = () => {
    setShowArchiveModal(true);
  };





  // 处理知识选择
  const handleArchiveSelect = (knowledge: any) => {
    // 将选中的知识内容插入到当前编辑位置
    const textarea = document.querySelector('textarea');
    if (textarea) {
      const cursorPos = textarea.selectionStart;
      const textBefore = content.substring(0, cursorPos);
      const textAfter = content.substring(textarea.selectionEnd);

      // 创建知识引用格式
      const knowledgeReference = `\n\n--- 知识：${knowledge.title} ---\n${knowledge.content}\n---\n\n`;

      // 更新内容
      const newContent = textBefore + knowledgeReference + textAfter;
      onChange(newContent);

      // 将光标移动到插入内容之后
      setTimeout(() => {
        textarea.selectionStart = cursorPos + knowledgeReference.length;
        textarea.selectionEnd = cursorPos + knowledgeReference.length;
        textarea.focus();
      }, 0);
    }
  };



  // 添加一个状态来控制是否使用A4宽度，从localStorage加载初始值
  const [isA4Width, setIsA4Width] = React.useState(() => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('editor_a4_width_mode');
      // 如果没有保存过设置，默认为true（A4模式）
      return savedState === null ? true : savedState === 'true';
    }
    return true; // 默认为A4模式
  });

  // 切换A4宽度模式并保存到localStorage
  const toggleA4Width = () => {
    setIsA4Width(prev => {
      const newValue = !prev;
      // 保存到localStorage，全局统一记忆
      if (typeof window !== 'undefined') {
        localStorage.setItem('editor_a4_width_mode', newValue.toString());
      }
      return newValue;
    });
  };

  // AI覆盖层面板状态
  const [showAIOverlay, setShowAIOverlay] = useState(false);

  return (
    <div className="flex-1 flex flex-col h-full bg-card-color rounded-xl overflow-hidden border border-[rgba(120,180,140,0.4)] shadow-md relative">
      <div className="p-4 border-b border-[rgba(120,180,140,0.3)] flex justify-center items-center">
        {/* 按钮组 - 居中显示 */}
        <div className="flex items-center space-x-2 overflow-x-auto scrollbar-thin pr-2" style={{ scrollbarWidth: 'thin' }}>
          <button
            onClick={() => {
              setSelectedText(''); // 清空选中文本
              setShowAIOverlay(true); // 打开覆盖层面板
            }}
            className="p-2.5 rounded-lg bg-[#88b892] hover:bg-[#78a882] active:bg-[#689872] transition-colors duration-200 flex items-center justify-center text-white w-20 sm:w-28 shrink-0"
            title="AI写作"
          >
            <span className="material-icons text-white text-sm md:text-base">smart_toy</span>
            <span className="ml-1 md:ml-1.5 text-xs md:text-sm font-medium whitespace-nowrap">AI写作</span>
          </button>

          <button
            onClick={openPolishModal}
            className="p-2.5 rounded-lg bg-[#d5a26f] hover:bg-[#c5925f] active:bg-[#b5824f] transition-colors duration-200 flex items-center justify-center text-white w-20 sm:w-28 shrink-0"
            title="选中润色"
          >
            <span className="material-icons text-white text-sm md:text-base">auto_fix_high</span>
            <span className="ml-1 md:ml-1.5 text-xs md:text-sm font-medium whitespace-nowrap">选中润色</span>
          </button>

          <button
            onClick={openArchiveModal}
            className="p-2.5 rounded-lg bg-[#7a9ec0] hover:bg-[#6a8eb0] active:bg-[#5a7ea0] transition-colors duration-200 flex items-center justify-center text-white w-20 sm:w-28 shrink-0"
            title="知识库"
          >
            <span className="material-icons text-white text-sm md:text-base">folder_special</span>
            <span className="ml-1 md:ml-1.5 text-xs md:text-sm font-medium whitespace-nowrap">知识库</span>
          </button>

          <button
            onClick={onOpenCharacterModal}
            className="p-2.5 rounded-lg bg-[#9c6fe0] hover:bg-[#8c5fd0] active:bg-[#7c4fc0] transition-colors duration-200 flex items-center justify-center text-white w-20 sm:w-28 shrink-0"
            title="角色卡"
          >
            <span className="material-icons text-white text-sm md:text-base">person</span>
            <span className="ml-1 md:ml-1.5 text-xs md:text-sm font-medium whitespace-nowrap">角色卡</span>
          </button>

          <button
            onClick={toggleA4Width}
            className="p-2.5 rounded-lg bg-[#a2c3b4] hover:bg-[#92b3a4] active:bg-[#82a394] transition-colors duration-200 flex items-center justify-center text-white w-20 sm:w-28 shrink-0"
            title={isA4Width ? "宽屏模式" : "A4页面模式"}
          >
            <span className="material-icons text-white text-sm md:text-base">{isA4Width ? "fullscreen" : "fit_screen"}</span>
            <span className="ml-1 md:ml-1.5 text-xs md:text-sm font-medium whitespace-nowrap">{isA4Width ? "切换宽屏" : "切换A4"}</span>
          </button>
        </div>
      </div>



      {/* 文本编辑区域 */}
      <div className="flex-1 overflow-auto relative">
        <div className={`h-full ${isA4Width ? 'flex justify-center' : 'editor-grid-bg'}`}>
          <textarea
            value={content}
            onChange={(e) => {
              // 正常更新内容，让React控制输入
              onChange(e.target.value);
            }}
            onCompositionEnd={(e) => {
              // 输入法组合结束后，再次更新内容以确保获取最终确认的文本
              console.log('输入法组合结束，确保内容更新');
              onChange(e.target.value);
            }}
            className={`h-full border-none focus:outline-none focus:ring-0 resize-none text-text-dark p-6 ${isA4Width ? 'editor-grid-bg' : ''}`}
            style={{
              fontFamily: "'思源黑体', 'Noto Sans SC', sans-serif",
              fontSize: '16pt',
              fontWeight: 400,
              lineHeight: '2.0',
              color: 'var(--text-dark)',
              width: isA4Width ? '21cm' : '100%',
              maxWidth: isA4Width ? '21cm' : 'none',
              backgroundColor: 'transparent',
              position: isA4Width ? 'relative' : 'absolute',
              left: isA4Width ? 'auto' : 0,
              right: isA4Width ? 'auto' : 0,
              top: isA4Width ? 'auto' : 0,
              bottom: isA4Width ? 'auto' : 0,
            }}
            placeholder={editMode === 'chapter' ? "开始创作你的故事..." :
                        editMode === 'outline' ? "请输入大纲内容..." :
                        editMode === 'setting' ? "请输入设定内容..." :
                        editMode === 'character' ? "请输入角色描述..." :
                        "请输入知识内容..."}
          ></textarea>
        </div>
      </div>

      {/* 底部信息栏 */}
      <div className="p-3 border-t border-[rgba(120,180,140,0.3)] bg-card-color flex justify-between items-center text-sm text-text-dark">
        <div className="flex items-center">
          <span className="material-icons text-sm mr-1 text-primary-green">history</span>
          {isSaving ? '正在保存...' : (lastSavedAt ? `已保存于 ${lastSavedAt.toLocaleTimeString()}` : '未保存')}
          <span className="mx-3 text-gray-400">|</span>
          {content ? `${content.length} 个字` : '0 个字'}
        </div>

        <div className="flex-grow"></div>
      </div>

      {/* AI覆盖层面板 */}
      <AIOverlayPanel
        isOpen={showAIOverlay}
        onClose={() => setShowAIOverlay(false)}
        onInsertToEditor={onInsertToEditor}
        currentContent={selectedText || content}
        chapters={chapters}
        activeChapter={activeChapter}
        initialPromptType={selectedText ? 'ai_polishing' : 'ai_writing'}
        workId={workId}
        defaultIsDescending={isDescending}
        onDataChange={() => {
          // 重新加载作品数据来同步状态
          loadWork();
        }}
        editMode={editMode}
      />

      {/* 知识库模态窗口 */}
      <KnowledgeModal
        isOpen={showArchiveModal}
        onClose={() => setShowArchiveModal(false)}
        onSelect={handleArchiveSelect}
        workId={workId}
      />


    </div>
  );
};

export default function WorkDetailPage() {
  const params = useParams();
  const router = useRouter();
  const workId = params?.id ? Number(params.id) : 0;

  // 所有状态定义
  const [work, setWork] = useState<Work | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSavedAt, setLastSavedAt] = useState<Date | null>(null);
  const [error, setError] = useState('');
  const [chapters, setChapters] = useState<Chapter[]>([{ order: 1, title: '', content: '' }]);
  const [activeChapter, setActiveChapter] = useState(0);
  // 章节排序状态，默认正序（升序）
  const [isDescending, setIsDescending] = useState<boolean>(() => {
    // 从 localStorage 加载排序状态，如果有的话
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem(`work_${workId}_chapter_order`);
      return savedState === 'desc';
    }
    return false;
  });

  // 上次保存的内容引用
  const lastSavedContent = useRef<string>('');
  // 保存计时器
  const saveTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 角色卡相关状态
  const [characters, setCharacters] = useState<Character[]>([]);
  const [activeCharacterId, setActiveCharacterId] = useState<string | null>(null);

  // 大纲相关状态
  const [outlines, setOutlines] = useState<Outline[]>([]);
  const [activeOutlineId, setActiveOutlineId] = useState<string | null>(null);

  // 设定相关状态
  const [settings, setSettings] = useState<Setting[]>([]);
  const [activeSettingId, setActiveSettingId] = useState<string | null>(null);

  // 知识库相关状态
  const [knowledges, setKnowledges] = useState<Knowledge[]>([]);
  const [activeKnowledgeId, setActiveKnowledgeId] = useState<number | null>(null);

  // 编辑模式状态
  const [editMode, setEditMode] = useState<EditMode>('chapter');
  const [showCharacterModal, setShowCharacterModal] = useState(false);

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      // 清除可能存在的定时器
      if (saveTimerRef.current) {
        clearTimeout(saveTimerRef.current);
        saveTimerRef.current = null;
      }
    };
  }, []);

  // 监听排序状态变化，保存到 localStorage
  useEffect(() => {
    if (typeof window !== 'undefined' && workId) {
      localStorage.setItem(`work_${workId}_chapter_order`, isDescending ? 'desc' : 'asc');
    }
  }, [isDescending, workId]);

  // 处理AI内容插入到编辑器
  const handleInsertToEditor = (content: string) => {
    // 获取textarea
    const textarea = document.querySelector('textarea');
    if (textarea && activeChapter >= 0 && activeChapter < chapters.length) {
      // 获取光标位置
      const cursorPos = textarea.selectionStart;
      const currentContent = textarea.value;

      // 插入内容
      const newContent = currentContent.substring(0, cursorPos) + content + currentContent.substring(cursorPos);

      // 更新章节
      const newChapters = [...chapters];
      newChapters[activeChapter] = {
        ...newChapters[activeChapter],
        content: newContent
      };
      setChapters(newChapters);

      // 立即保存
      handleSave(newChapters);

      // 将光标移动到插入内容之后
      setTimeout(() => {
        textarea.selectionStart = cursorPos + content.length;
        textarea.selectionEnd = cursorPos + content.length;
        textarea.focus();
      }, 0);
    }
  };



  // 统一的内容变更处理
  const handleUnifiedContentChange = (newContent: string) => {
    switch (editMode) {
      case 'outline':
        if (activeOutlineId) {
          const updatedOutlines = outlines.map(outline =>
            outline.id === activeOutlineId ? { ...outline, content: newContent } : outline
          );
          setOutlines(updatedOutlines);
        }
        break;
      case 'setting':
        if (activeSettingId) {
          const updatedSettings = settings.map(setting =>
            setting.id === activeSettingId ? { ...setting, content: newContent } : setting
          );
          setSettings(updatedSettings);
        }
        break;
      case 'character':
        if (activeCharacterId) {
          // 解析角色内容格式
          const lines = newContent.split('\n');
          let gender = '', personality = '', background = '';
          let currentSection = '';

          for (const line of lines) {
            if (line.startsWith('性别：')) {
              gender = line.replace('性别：', '').trim();
            } else if (line.startsWith('性格：')) {
              currentSection = 'personality';
            } else if (line.startsWith('背景：')) {
              currentSection = 'background';
            } else if (line.trim() && currentSection === 'personality') {
              personality += (personality ? '\n' : '') + line;
            } else if (line.trim() && currentSection === 'background') {
              background += (background ? '\n' : '') + line;
            }
          }

          const updatedCharacters = characters.map(char =>
            char.id === activeCharacterId ? {
              ...char,
              gender: gender || char.gender,
              personality: personality || char.personality,
              background: background || char.background
            } : char
          );
          setCharacters(updatedCharacters);
        }
        break;
      case 'knowledge':
        if (activeKnowledgeId) {
          const updatedKnowledges = knowledges.map(knowledge =>
            knowledge.id === activeKnowledgeId ? { ...knowledge, content: newContent } : knowledge
          );
          setKnowledges(updatedKnowledges);
        }
        break;
      default: // chapter
        handleChange(newContent);
        break;
    }
  };

  // 统一的标题变更处理
  const handleUnifiedTitleChange = (newTitle: string) => {
    switch (editMode) {
      case 'outline':
        if (activeOutlineId) {
          const updatedOutlines = outlines.map(outline =>
            outline.id === activeOutlineId ? { ...outline, title: newTitle } : outline
          );
          setOutlines(updatedOutlines);
        }
        break;
      case 'setting':
        if (activeSettingId) {
          const updatedSettings = settings.map(setting =>
            setting.id === activeSettingId ? { ...setting, title: newTitle } : setting
          );
          setSettings(updatedSettings);
        }
        break;
      case 'character':
        if (activeCharacterId) {
          const updatedCharacters = characters.map(char =>
            char.id === activeCharacterId ? { ...char, name: newTitle } : char
          );
          setCharacters(updatedCharacters);
        }
        break;
      case 'knowledge':
        if (activeKnowledgeId) {
          const updatedKnowledges = knowledges.map(knowledge =>
            knowledge.id === activeKnowledgeId ? { ...knowledge, title: newTitle } : knowledge
          );
          setKnowledges(updatedKnowledges);
        }
        break;
      default: // chapter
        handleTitleChange(newTitle);
        break;
    }
  };

  // 统一的保存处理
  const handleUnifiedSave = () => {
    switch (editMode) {
      case 'outline':
        if (activeOutlineId) {
          const selectedOutline = outlines.find(outline => outline.id === activeOutlineId);
          if (selectedOutline) {
            handleOutlineSave(selectedOutline);
          }
        }
        break;
      case 'setting':
        if (activeSettingId) {
          const selectedSetting = settings.find(setting => setting.id === activeSettingId);
          if (selectedSetting) {
            handleSettingSave(selectedSetting);
          }
        }
        break;
      case 'character':
        if (activeCharacterId) {
          const selectedCharacter = characters.find(char => char.id === activeCharacterId);
          if (selectedCharacter) {
            handleCharacterSave(selectedCharacter);
          }
        }
        break;
      case 'knowledge':
        // 知识库保存逻辑待实现
        break;
      default: // chapter
        handleSave();
        break;
    }
  };

  // 触发保存的函数
  const triggerSave = useCallback(async () => {
    if (!work) return;

    // 获取当前内容的序列化字符串
    const currentContent = workContentUtils.stringifyChapters(chapters);

    // 只有当内容有变化时才保存
    if (currentContent !== lastSavedContent.current) {
      setIsSaving(true);

      try {
        const updatedWork = {
          ...work,
          content: currentContent,
          updatedAt: new Date()
        };

        await updateWork(updatedWork);
        setWork(updatedWork);
        setLastSavedAt(new Date());
        // 更新上次保存的内容
        lastSavedContent.current = currentContent;
        console.log('保存成功', new Date().toLocaleTimeString());
      } catch (error) {
        console.error('保存失败:', error);
        setError('保存失败');
      } finally {
        setIsSaving(false);
      }
    }
  }, [work, chapters]);

  // 安排延迟保存
  const scheduleSave = useCallback(() => {
    // 取消已有的定时器
    if (saveTimerRef.current) {
      clearTimeout(saveTimerRef.current);
    }

    // 设置新的定时器，1秒后保存
    saveTimerRef.current = setTimeout(() => {
      triggerSave();
      saveTimerRef.current = null;
    }, 1000);
  }, [triggerSave]);

  // 加载角色数据
  const loadCharacters = async () => {
    try {
      const { getCharactersByWorkId } = await import('@/data');
      const workCharacters = await getCharactersByWorkId(workId);
      setCharacters(workCharacters);
    } catch (error) {
      console.error('加载角色数据失败:', error);
    }
  };

  // 加载大纲数据
  const loadOutlines = async () => {
    try {
      const { getOutlinesByWorkId } = await import('@/data');
      const workOutlines = await getOutlinesByWorkId(workId);
      setOutlines(workOutlines);
    } catch (error) {
      console.error('加载大纲数据失败:', error);
    }
  };

  // 加载设定数据
  const loadSettings = async () => {
    try {
      const { getSettingsByWorkId } = await import('@/data');
      const workSettings = await getSettingsByWorkId(workId);
      setSettings(workSettings);
    } catch (error) {
      console.error('加载设定数据失败:', error);
    }
  };

  // 加载知识库数据
  const loadKnowledges = async () => {
    try {
      // 使用现有的知识库数据获取方法
      const { getAllArchives } = await import('@/data');
      const allKnowledges = await getAllArchives();
      // 过滤当前作品的知识库
      const workKnowledges = allKnowledges.filter(knowledge => knowledge.workId === workId);
      setKnowledges(workKnowledges);
    } catch (error) {
      console.error('加载知识库数据失败:', error);
    }
  };

  // 角色点击处理
  const handleCharacterClick = (characterId: string) => {
    setActiveCharacterId(characterId);
    setActiveOutlineId(null);
    setActiveSettingId(null);
    setActiveKnowledgeId(null);
    setEditMode('character');
  };

  // 大纲点击处理
  const handleOutlineClick = (outlineId: string) => {
    setActiveOutlineId(outlineId);
    setActiveCharacterId(null);
    setActiveSettingId(null);
    setActiveKnowledgeId(null);
    setEditMode('outline');
  };

  // 设定点击处理
  const handleSettingClick = (settingId: string) => {
    setActiveSettingId(settingId);
    setActiveCharacterId(null);
    setActiveOutlineId(null);
    setActiveKnowledgeId(null);
    setEditMode('setting');
  };

  // 知识库点击处理
  const handleKnowledgeClick = (knowledgeId: number) => {
    setActiveKnowledgeId(knowledgeId);
    setActiveCharacterId(null);
    setActiveOutlineId(null);
    setActiveSettingId(null);
    setEditMode('knowledge');
  };

  // 获取当前编辑的内容和标题
  const getCurrentEditContentData = () => {
    return getCurrentEditContent({
      editMode,
      chapters,
      activeChapter,
      characters,
      activeCharacterId,
      outlines,
      activeOutlineId,
      settings,
      activeSettingId,
      knowledges,
      activeKnowledgeId
    });
  };

  // 章节点击处理（修改原有函数）
  const handleChapterClickInternal = (index: number) => {
    setActiveChapter(index);
    setActiveCharacterId(null);
    setActiveOutlineId(null);
    setActiveSettingId(null);
    setActiveKnowledgeId(null);
    setEditMode('chapter');
  };

  // 添加新角色
  const handleAddCharacter = async () => {
    const newCharacter: Character = {
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      name: '新角色',
      gender: '无',
      personality: '',
      background: '',
      workId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    try {
      const { addCharacter } = await import('@/data');
      await addCharacter(newCharacter);
      const updatedCharacters = [...characters, newCharacter];
      setCharacters(updatedCharacters);
      setActiveCharacterId(newCharacter.id);
      setEditMode('character');
    } catch (error) {
      console.error('创建角色失败:', error);
    }
  };

  // 角色保存处理
  const handleCharacterSave = async (updatedCharacter: Character) => {
    try {
      const { updateCharacter } = await import('@/data');
      await updateCharacter(updatedCharacter);
      const updatedCharacters = characters.map(char =>
        char.id === updatedCharacter.id ? updatedCharacter : char
      );
      setCharacters(updatedCharacters);
      setLastSavedAt(new Date());
    } catch (error) {
      console.error('保存角色失败:', error);
    }
  };

  // 角色名称编辑处理
  const handleEditCharacterName = async (characterId: string, newName: string) => {
    const updatedCharacters = characters.map(char =>
      char.id === characterId ? { ...char, name: newName.trim() || '未命名角色', updatedAt: new Date() } : char
    );

    try {
      const { updateCharacter } = await import('@/data');
      const characterToUpdate = updatedCharacters.find(char => char.id === characterId);
      if (characterToUpdate) {
        await updateCharacter(characterToUpdate);
        setCharacters(updatedCharacters);
      }
    } catch (error) {
      console.error('更新角色名称失败:', error);
    }
  };

  // 角色删除处理
  const handleDeleteCharacter = async (characterId: string) => {
    try {
      const { deleteCharacter } = await import('@/data');
      await deleteCharacter(characterId);

      const updatedCharacters = characters.filter(char => char.id !== characterId);
      setCharacters(updatedCharacters);

      // 如果删除的是当前激活的角色，清除激活状态
      if (activeCharacterId === characterId) {
        setActiveCharacterId(null);
        setEditMode('chapter');
      }
    } catch (error) {
      console.error('删除角色失败:', error);
    }
  };

  // 大纲编辑和删除处理
  const handleEditOutlineTitle = async (outlineId: string, newTitle: string) => {
    const updatedOutlines = outlines.map(outline =>
      outline.id === outlineId ? { ...outline, title: newTitle.trim() || '未命名大纲', updatedAt: new Date() } : outline
    );

    try {
      const { updateOutline } = await import('@/data');
      const outlineToUpdate = updatedOutlines.find(outline => outline.id === outlineId);
      if (outlineToUpdate) {
        await updateOutline(outlineToUpdate);
        setOutlines(updatedOutlines);
      }
    } catch (error) {
      console.error('更新大纲标题失败:', error);
    }
  };

  const handleDeleteOutline = async (outlineId: string) => {
    try {
      const { deleteOutline } = await import('@/data');
      await deleteOutline(outlineId);

      const updatedOutlines = outlines.filter(outline => outline.id !== outlineId);
      setOutlines(updatedOutlines);

      if (activeOutlineId === outlineId) {
        setActiveOutlineId(null);
        setEditMode('chapter');
      }
    } catch (error) {
      console.error('删除大纲失败:', error);
    }
  };

  // 设定编辑和删除处理
  const handleEditSettingTitle = async (settingId: string, newTitle: string) => {
    const updatedSettings = settings.map(setting =>
      setting.id === settingId ? { ...setting, title: newTitle.trim() || '未命名设定', updatedAt: new Date() } : setting
    );

    try {
      const { updateSetting } = await import('@/data');
      const settingToUpdate = updatedSettings.find(setting => setting.id === settingId);
      if (settingToUpdate) {
        await updateSetting(settingToUpdate);
        setSettings(updatedSettings);
      }
    } catch (error) {
      console.error('更新设定标题失败:', error);
    }
  };

  const handleDeleteSetting = async (settingId: string) => {
    try {
      const { deleteSetting } = await import('@/data');
      await deleteSetting(settingId);

      const updatedSettings = settings.filter(setting => setting.id !== settingId);
      setSettings(updatedSettings);

      if (activeSettingId === settingId) {
        setActiveSettingId(null);
        setEditMode('chapter');
      }
    } catch (error) {
      console.error('删除设定失败:', error);
    }
  };

  // 知识库编辑和删除处理
  const handleEditKnowledgeTitle = async (knowledgeId: number, newTitle: string) => {
    try {
      const { updateArchive } = await import('@/data');
      const targetKnowledge = knowledges.find(k => k.id === knowledgeId);
      if (!targetKnowledge) return;

      const updatedKnowledge = {
        ...targetKnowledge,
        title: newTitle.trim() || '未命名知识',
        updatedAt: new Date()
      };

      await updateArchive(updatedKnowledge);
      const updatedKnowledges = knowledges.map(knowledge =>
        knowledge.id === knowledgeId ? updatedKnowledge : knowledge
      );
      setKnowledges(updatedKnowledges);
    } catch (error) {
      console.error('更新知识库标题失败:', error);
    }
  };

  const handleDeleteKnowledge = async (knowledgeId: number) => {
    try {
      const { deleteArchive } = await import('@/data');
      await deleteArchive(knowledgeId);

      const updatedKnowledges = knowledges.filter(knowledge => knowledge.id !== knowledgeId);
      setKnowledges(updatedKnowledges);

      if (activeKnowledgeId === knowledgeId) {
        setActiveKnowledgeId(null);
        setEditMode('chapter');
      }
    } catch (error) {
      console.error('删除知识库失败:', error);
    }
  };

  // 添加新大纲
  const handleAddOutline = async () => {
    const newOutline: Outline = {
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      title: '新大纲',
      content: '',
      order: outlines.length + 1,
      workId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    try {
      const { addOutline } = await import('@/data');
      await addOutline(newOutline);
      const updatedOutlines = [...outlines, newOutline];
      setOutlines(updatedOutlines);
      setActiveOutlineId(newOutline.id);
      setEditMode('outline');
    } catch (error) {
      console.error('创建大纲失败:', error);
    }
  };

  // 大纲保存处理
  const handleOutlineSave = (updatedOutline: Outline) => {
    const updatedOutlines = outlines.map(outline =>
      outline.id === updatedOutline.id ? updatedOutline : outline
    );
    setOutlines(updatedOutlines);
    setLastSavedAt(new Date());
  };

  // 添加新设定
  const handleAddSetting = async () => {
    const newSetting: Setting = {
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      title: '新设定',
      content: '',
      category: 'other',
      order: settings.length + 1,
      workId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    try {
      const { addSetting } = await import('@/data');
      await addSetting(newSetting);
      const updatedSettings = [...settings, newSetting];
      setSettings(updatedSettings);
      setActiveSettingId(newSetting.id);
      setEditMode('setting');
    } catch (error) {
      console.error('创建设定失败:', error);
    }
  };

  // 设定保存处理
  const handleSettingSave = async (updatedSetting: Setting) => {
    try {
      const { updateSetting } = await import('@/data');
      await updateSetting(updatedSetting);
      const updatedSettings = settings.map(setting =>
        setting.id === updatedSetting.id ? updatedSetting : setting
      );
      setSettings(updatedSettings);
      setLastSavedAt(new Date());
    } catch (error) {
      console.error('保存设定失败:', error);
    }
  };

  // 添加新知识库
  const handleAddKnowledge = async () => {
    try {
      const { addArchive } = await import('@/data');
      const newKnowledge: Knowledge = {
        title: '新知识',
        content: '',
        workId,
        tags: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const savedKnowledge = await addArchive(newKnowledge);
      const updatedKnowledges = [...knowledges, savedKnowledge];
      setKnowledges(updatedKnowledges);
      setActiveKnowledgeId(savedKnowledge.id!);
      setEditMode('knowledge');
    } catch (error) {
      console.error('创建知识库失败:', error);
    }
  };

  // 知识库保存处理
  const handleKnowledgeSave = (updatedKnowledge: Knowledge) => {
    const updatedKnowledges = knowledges.map(knowledge =>
      knowledge.id === updatedKnowledge.id ? updatedKnowledge : knowledge
    );
    setKnowledges(updatedKnowledges);
    setLastSavedAt(new Date());
  };

  // 打开角色卡模态窗口
  const openCharacterModal = () => {
    setShowCharacterModal(true);
  };

  // 获取作品数据
  useEffect(() => {
    const fetchWork = async () => {
      if (!workId) return;

      try {
        // 首先尝试重新创建数据库以确保所有表都存在
        try {
          const { recreateMainDatabase } = await import('@/data');
          // 只在首次访问时重新创建数据库
          const hasRecreated = localStorage.getItem('db_recreated_v9');
          if (!hasRecreated) {
            console.log('首次访问，重新创建数据库以确保所有表存在...');
            await recreateMainDatabase();
            localStorage.setItem('db_recreated_v9', 'true');
          }
        } catch (dbError) {
          console.warn('数据库重新创建失败，继续使用现有数据库:', dbError);
        }

        const workData = await getWorkById(workId);
        if (!workData) {
          router.push('/works');
          return;
        }

        setWork(workData);
        const parsedChapters = workContentUtils.parseContent(workData.content);
        setChapters(parsedChapters);

        // 根据排序状态设置初始激活章节
        if (parsedChapters.length > 0) {
          if (isDescending) {
            // 倒序：激活最后一章（索引为 length - 1）
            setActiveChapter(parsedChapters.length - 1);
          } else {
            // 正序：激活第一章（索引为 0）
            setActiveChapter(0);
          }
        }

        // 初始化上次保存的内容
        lastSavedContent.current = workData.content;
        setLastSavedAt(new Date(workData.updatedAt));

        // 加载所有数据
        await loadCharacters();
        await loadOutlines();
        await loadSettings();
        await loadKnowledges();
      } catch (error) {
        console.error('获取作品失败:', error);
        setError('获取作品失败');
      } finally {
        setIsLoading(false);
      }
    };

    fetchWork();
  }, [workId, router]);

  // 如果处于加载状态，渲染加载组件
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="w-12 h-12 bg-blue-200 rounded-full mb-4"></div>
          <div className="h-4 w-24 bg-blue-200 rounded"></div>
        </div>
      </div>
    );
  }

  // 如果作品不存在，渲染错误组件
  if (!work) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50">
        <div className="text-center">
          <span className="material-icons text-4xl text-gray-300 mb-3">error</span>
          <p className="text-gray-500">作品不存在或已被删除</p>
          <button
            className="btn-primary mt-4"
            onClick={() => router.push('/works')}
          >
            返回作品列表
          </button>
        </div>
      </div>
    );
  }

  // 处理章节点击事件
  const handleChapterClickEvent = (index: number) => {
    // 保存当前章节
    scheduleSave();

    // 切换章节
    handleChapterClickInternal(index);

    // 在切换章节后重置滚动位置
    setTimeout(() => {
      const editorTextarea = document.querySelector('.flex-1.overflow-auto textarea') as HTMLTextAreaElement;
      if (editorTextarea) {
        editorTextarea.scrollTop = 0;
      }
    }, 0);
  };

  // 处理章节内容变更
  const handleChange = (content: string) => {
    if (activeChapter >= 0 && activeChapter < chapters.length) {
      const newChapters = [...chapters];
      newChapters[activeChapter] = {
        ...newChapters[activeChapter],
        content: content
      };
      setChapters(newChapters);

      // 安排延迟保存
      scheduleSave();
    }
  };

  // 处理章节标题变更
  const handleTitleChange = (title: string) => {
    if (activeChapter >= 0 && activeChapter < chapters.length) {
      const newChapters = [...chapters];
      newChapters[activeChapter] = {
        ...newChapters[activeChapter],
        title: title
      };
      setChapters(newChapters);

      // 安排延迟保存
      scheduleSave();
    }
  };

  const handleAddChapter = () => {
    // 获取下一个order编号
    const getNextOrder = () => {
      if (chapters.length === 0) return 1;

      const maxOrder = Math.max(...chapters.map(chapter => chapter.order));
      return maxOrder + 1;
    };

    const nextOrder = getNextOrder();
    const newChapter = {
      order: nextOrder,
      title: `第${nextOrder}章`,
      content: ''
    };
    const newChapters = [...chapters, newChapter];

    // 先更新章节列表
    setChapters(newChapters);
    // 设置激活章节为新添加的章节
    setActiveChapter(chapters.length);

    // 立即保存新章节
    if (work) {
      setIsSaving(true);

      const updatedContent = workContentUtils.stringifyChapters(newChapters);

      try {
        const updatedWork = {
          ...work,
          content: updatedContent,
          updatedAt: new Date()
        };

        updateWork(updatedWork)
          .then(() => {
            setWork(updatedWork);
            setLastSavedAt(new Date());
            // 更新最后保存的内容
            lastSavedContent.current = updatedContent;
            console.log('新章节保存成功', new Date().toLocaleTimeString());
          })
          .catch(err => {
            console.error('保存新章节失败:', err);
            setError('保存新章节失败');
          })
          .finally(() => {
            setIsSaving(false);
          });
      } catch (error) {
        console.error('保存新章节失败:', error);
        setError('保存新章节失败');
        setIsSaving(false);
      }
    }
  };

  const handleSave = async (chaptersToSave?: Chapter[]) => {
    // 清除任何挂起的自动保存
    if (saveTimerRef.current) {
      clearTimeout(saveTimerRef.current);
      saveTimerRef.current = null;
    }

    // 立即执行保存
    if (!work) return;

    setIsSaving(true);
    // 使用传入的chapters或当前的chapters
    const targetChapters = chaptersToSave || chapters;
    const currentContent = workContentUtils.stringifyChapters(targetChapters);

    try {
      const updatedWork = {
        ...work,
        content: currentContent,
        updatedAt: new Date()
      };

      await updateWork(updatedWork);
      setWork(updatedWork);
      setLastSavedAt(new Date());
      // 更新上次保存的内容
      lastSavedContent.current = currentContent;

      // 如果传入了新的chapters，确保本地状态也更新
      if (chaptersToSave && chaptersToSave !== chapters) {
        setChapters(chaptersToSave);
      }
    } catch (error) {
      console.error('保存作品失败:', error);
      setError('保存作品失败');
    } finally {
      setIsSaving(false);
    }
  };

  // 删除章节
  const handleDeleteChapter = async (index: number) => {
    if (chapters.length <= 1) {
      setError('至少需要保留一个章节');
      return;
    }

    const newChapters = chapters.filter((_, i) => i !== index);
    setChapters(newChapters);

    // 调整当前激活章节
    if (activeChapter >= newChapters.length) {
      setActiveChapter(newChapters.length - 1);
    } else if (activeChapter > index) {
      setActiveChapter(activeChapter - 1);
    }

    // 立即保存
    if (work) {
      setIsSaving(true);
      const updatedContent = workContentUtils.stringifyChapters(newChapters);

      try {
        const updatedWork = {
          ...work,
          content: updatedContent,
          updatedAt: new Date()
        };

        await updateWork(updatedWork);
        setWork(updatedWork);
        setLastSavedAt(new Date());
        lastSavedContent.current = updatedContent;
        console.log('章节删除成功', new Date().toLocaleTimeString());
      } catch (error) {
        console.error('删除章节失败:', error);
        setError('删除章节失败');
      } finally {
        setIsSaving(false);
      }
    }
  };

  // 编辑章节标题
  const handleEditChapterTitle = async (index: number, newTitle: string) => {
    if (index < 0 || index >= chapters.length) return;

    const newChapters = [...chapters];
    newChapters[index] = {
      ...newChapters[index],
      title: newTitle
    };
    setChapters(newChapters);

    // 立即保存
    if (work) {
      setIsSaving(true);
      const updatedContent = workContentUtils.stringifyChapters(newChapters);

      try {
        const updatedWork = {
          ...work,
          content: updatedContent,
          updatedAt: new Date()
        };

        await updateWork(updatedWork);
        setWork(updatedWork);
        setLastSavedAt(new Date());
        lastSavedContent.current = updatedContent;
        console.log('章节标题更新成功', new Date().toLocaleTimeString());
      } catch (error) {
        console.error('更新章节标题失败:', error);
        setError('更新章节标题失败');
      } finally {
        setIsSaving(false);
      }
    }
  };

  // 移动章节
  const handleMoveChapter = async (fromIndex: number, toIndex: number) => {
    if (fromIndex === toIndex || fromIndex < 0 || toIndex < 0 ||
        fromIndex >= chapters.length || toIndex >= chapters.length) {
      return;
    }

    const newChapters = [...chapters];
    const [movedChapter] = newChapters.splice(fromIndex, 1);
    newChapters.splice(toIndex, 0, movedChapter);

    // 重新分配order值，确保连续性
    const reorderedChapters = newChapters.map((chapter, index) => ({
      ...chapter,
      order: index + 1
    }));

    setChapters(reorderedChapters);

    // 更新当前激活章节的索引
    let newActiveChapter = activeChapter;
    if (activeChapter === fromIndex) {
      // 如果移动的是当前激活章节
      newActiveChapter = toIndex;
    } else if (activeChapter > fromIndex && activeChapter <= toIndex) {
      // 如果当前激活章节在移动范围内，向前移动
      newActiveChapter = activeChapter - 1;
    } else if (activeChapter < fromIndex && activeChapter >= toIndex) {
      // 如果当前激活章节在移动范围内，向后移动
      newActiveChapter = activeChapter + 1;
    }
    setActiveChapter(newActiveChapter);

    // 立即保存
    if (work) {
      setIsSaving(true);
      const updatedContent = workContentUtils.stringifyChapters(reorderedChapters);

      try {
        const updatedWork = {
          ...work,
          content: updatedContent,
          updatedAt: new Date()
        };

        await updateWork(updatedWork);
        setWork(updatedWork);
        setLastSavedAt(new Date());
        lastSavedContent.current = updatedContent;
        console.log('章节移动成功', new Date().toLocaleTimeString());
      } catch (error) {
        console.error('移动章节失败:', error);
        setError('移动章节失败');
      } finally {
        setIsSaving(false);
      }
    }
  };

  return (
    <div className="flex h-screen bg-bg-color animate-fadeIn overflow-hidden">
      {/* 背景网格 */}
      <div className="grid-background"></div>

      {/* 装饰元素，在小屏幕上减少数量 */}
      <div className="dot hidden md:block" style={{ top: "120px", left: "15%" }}></div>
      <div className="dot" style={{ bottom: "80px", right: "20%" }}></div>
      <div className="dot hidden md:block" style={{ top: "30%", right: "25%" }}></div>
      <div className="dot hidden md:block" style={{ bottom: "40%", left: "30%" }}></div>

      <svg className="wave hidden md:block" style={{ bottom: "20px", left: "10%" }} width="100" height="20" viewBox="0 0 100 20">
        <path d="M0,10 Q25,0 50,10 T100,10" fill="none" stroke="var(--accent-brown)" strokeWidth="2" />
      </svg>

      <svg className="wave hidden md:block" style={{ top: "15%", right: "5%" }} width="100" height="20" viewBox="0 0 100 20">
        <path d="M0,10 Q25,0 50,10 T100,10" fill="none" stroke="var(--accent-brown)" strokeWidth="2" />
      </svg>

      {/* 左侧导航栏 */}
      <Sidebar
        activeMenu="works"
        chapters={chapters}
        activeChapter={activeChapter}
        onChapterClick={handleChapterClickEvent}
        onAddChapter={handleAddChapter}
        onDeleteChapter={handleDeleteChapter}
        onEditChapterTitle={handleEditChapterTitle}
        onMoveChapter={handleMoveChapter}
        isDescending={isDescending}
        setIsDescending={setIsDescending}
        characters={characters}
        activeCharacter={activeCharacterId}
        onCharacterClick={handleCharacterClick}
        onAddCharacter={handleAddCharacter}
        onEditCharacterName={handleEditCharacterName}
        onDeleteCharacter={handleDeleteCharacter}
        outlines={outlines}
        activeOutline={activeOutlineId}
        onOutlineClick={handleOutlineClick}
        onAddOutline={handleAddOutline}
        onEditOutlineTitle={handleEditOutlineTitle}
        onDeleteOutline={handleDeleteOutline}
        settings={settings}
        activeSetting={activeSettingId}
        onSettingClick={handleSettingClick}
        onAddSetting={handleAddSetting}
        onEditSettingTitle={handleEditSettingTitle}
        onDeleteSetting={handleDeleteSetting}
        knowledges={knowledges}
        activeKnowledge={activeKnowledgeId}
        onKnowledgeClick={handleKnowledgeClick}
        onAddKnowledge={handleAddKnowledge}
        onEditKnowledgeTitle={handleEditKnowledgeTitle}
        onDeleteKnowledge={handleDeleteKnowledge}
      />

      {/* 中间内容区 */}
      <div className="flex-1 flex flex-col main-content-area">
        {/* 使用通用顶边栏组件 */}
        <TopBar
          showBackButton={true}
          actions={
            <>
              <span className={`badge ${
                work.type === 'novel' ? 'badge-blue' :
                work.type === 'character' ? 'badge-purple' :
                work.type === 'worldbuilding' ? 'badge-green' :
                'badge-yellow'
              }`}>
                {work.type === 'novel' ? '小说' :
                work.type === 'character' ? '角色' :
                work.type === 'worldbuilding' ? '世界' :
                '情节'}
              </span>
            </>
          }
        />

        {/* 编辑器区域 */}
        <div className="flex-1 flex overflow-hidden p-4 md:p-6 lg:p-8">
          <div className="flex-1 flex rounded-xl overflow-hidden shadow-lg bg-card-color border border-[rgba(120,180,140,0.2)] relative">
            {(() => {
              const currentEdit = getCurrentEditContentData();

              // 检查是否有有效的编辑内容
              const hasValidContent = editMode === 'chapter' ||
                (editMode === 'outline' && activeOutlineId && currentEdit.data) ||
                (editMode === 'setting' && activeSettingId && currentEdit.data) ||
                (editMode === 'character' && activeCharacterId && currentEdit.data) ||
                (editMode === 'knowledge' && activeKnowledgeId && currentEdit.data);

              if (!hasValidContent) {
                return (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <p>
                      {editMode === 'outline' ? '大纲不存在' :
                       editMode === 'setting' ? '设定不存在' :
                       editMode === 'character' ? '角色不存在' :
                       editMode === 'knowledge' ? '知识库不存在' :
                       '请选择要编辑的内容'}
                    </p>
                  </div>
                );
              }

              return (
                <RichTextEditor
                  content={currentEdit.content}
                  onChange={handleUnifiedContentChange}
                  onSave={handleUnifiedSave}
                  isSaving={isSaving}
                  chapters={chapters}
                  activeChapter={activeChapter}
                  workId={workId}
                  lastSavedAt={lastSavedAt}
                  isDescending={isDescending}
                  onOpenCharacterModal={openCharacterModal}
                  onInsertToEditor={handleInsertToEditor}
                  editMode={editMode}
                />
              );
            })()}
          </div>
        </div>
      </div>

      {/* 角色卡模态窗口 */}
      <CharacterCardModal
        isOpen={showCharacterModal}
        onClose={() => setShowCharacterModal(false)}
        workId={workId}
      />

      {/* 错误提示 */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-card-color border border-[rgba(224,149,117,0.5)] rounded-xl p-4 shadow-md animate-fadeIn">
          <div className="flex items-center">
            <span className="material-icons text-[#E0976F] mr-2">error</span>
            <span className="text-text-dark">{error}</span>
            <button
              className="ml-4 text-text-dark hover:text-[#E0976F]"
              onClick={() => setError('')}
            >
              <span className="material-icons">close</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}